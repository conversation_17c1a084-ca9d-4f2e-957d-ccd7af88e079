import { supabaseAdminQuery, supabaseAdmin } from './supabase'
import type { Database } from './supabase'

// Type aliases for better readability
type Department = Database['public']['Tables']['appy_departments']['Row']
type Employee = Database['public']['Tables']['appy_employees']['Row']
type Manager = Database['public']['Tables']['appy_managers']['Row']
type AppraisalPeriod = Database['public']['Tables']['appy_appraisal_periods']['Row']
type Appraisal = Database['public']['Tables']['appy_appraisals']['Row']

// Extended types with joined data
export interface EmployeeWithDepartment extends Employee {
  department_name: string
  manager_name: string | null
}

export interface AppraisalWithDetails extends Appraisal {
  employee_name: string
  manager_name: string
  department_name: string
}

// Query result type
interface QueryResult<T> {
  rows: T[]
  rowCount: number
}

// Helper function to format Supabase response to match PostgreSQL format
function formatSupabaseResponse<T>(data: T[] | null, error: any): QueryResult<T> {
  if (error) {
    // console.error('Supabase query error:', error)
    throw new Error(error.message || 'Database query failed')
  }
  
  return {
    rows: data || [],
    rowCount: data?.length || 0
  }
}

// Execute a query with logging (for compatibility)
export async function query<T = any>(
  description: string,
  queryFn: () => Promise<{ data: T[] | null; error: any }>
): Promise<QueryResult<T>> {
  const start = Date.now()
  
  try {
    const { data, error } = await queryFn()
    const duration = Date.now() - start
    
    if (process.env.NODE_ENV === 'development') {
      console.log('Executed query', { 
        description,
        duration: `${duration}ms`,
        rows: data?.length || 0
      })
    }
    
    return formatSupabaseResponse(data, error)
  } catch (error) {
    // console.error('Database query error:', {
    //   description,
    //   error: error instanceof Error ? error.message : error
    // })
    throw error
  }
}

// Health check function
export async function healthCheck(): Promise<boolean> {
  try {
    const { data, error } = await supabaseAdmin.from('appy_departments').select('id').limit(1)
    return !error && data !== null
  } catch (error) {
    // console.error('Database health check failed:', error)
    return false
  }
}

// Database-specific query builders
export const db = {
  // Departments
  async getDepartments(): Promise<Department[]> {
    const { data, error } = await supabaseAdmin.from('appy_departments')
      .select('id, name, created_at')
      .order('name')
    
    if (error) {
      // console.error('Failed to fetch departments:', error)
      throw new Error(error.message)
    }
    
    return data || []
  },

  async createDepartment(name: string): Promise<Department> {
    const { data, error } = await supabaseAdmin.from('appy_departments')
      .insert({ name })
      .select()
      .single()
    
    if (error) {
      // console.error('Failed to create department:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async updateDepartment(id: string, name: string): Promise<Department> {
    const { data, error } = await supabaseAdmin.from('appy_departments')
      .update({ name })
      .eq('id', id)
      .select()
      .single()
    
    if (error) {
      // console.error('Failed to update department:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async deleteDepartment(id: string): Promise<void> {
    const { error } = await supabaseAdmin.from('appy_departments')
      .delete()
      .eq('id', id)
    
    if (error) {
      // console.error('Failed to delete department:', error)
      throw new Error(error.message)
    }
  },

  // Employees
  async getEmployees(): Promise<EmployeeWithDepartment[]> {
    console.log('🔍 [DEBUG] Starting getEmployees() function')

    // Fetch employees with departments only (no manager join due to missing FK constraint)
    const { data: employees, error: employeesError } = await supabaseAdmin.from('appy_employees')
      .select(`
        *,
        appy_departments:department_id (
          id,
          name
        )
      `)
      .eq('active', true)
      .order('full_name')

    if (employeesError) {
      // console.error('Failed to fetch employees:', employeesError)
      throw new Error(employeesError.message)
    }

    console.log('👥 [DEBUG] Fetched employees:', employees?.map(emp => ({
      id: emp.id,
      full_name: emp.full_name,
      manager_id: emp.manager_id
    })))

    // Fetch all managers separately
    const { data: managers, error: managersError } = await supabaseAdmin.from('appy_managers')
      .select('user_id, full_name')
      .eq('active', true)

    if (managersError) {
      // console.error('Failed to fetch managers:', managersError)
      throw new Error(managersError.message)
    }

    console.log('👨‍💼 [DEBUG] Fetched managers:', managers?.map(mgr => ({
      user_id: mgr.user_id,
      full_name: mgr.full_name
    })))

    // Create a lookup map for managers
    const managerMap = new Map(
      (managers || []).map(manager => [manager.user_id, manager.full_name])
    )

    console.log('🗺️ [DEBUG] Manager lookup map:', Array.from(managerMap.entries()))

    // Transform the data to match expected format
    const result = (employees || []).map(employee => {
      const managerName = employee.manager_id ? managerMap.get(employee.manager_id) || null : null

      console.log(`🔗 [DEBUG] Employee ${employee.full_name}: manager_id="${employee.manager_id}" -> manager_name="${managerName}"`)

      return {
        ...employee,
        department_name: employee.appy_departments?.name || '',
        manager_name: managerName
      }
    })

    console.log('✅ [DEBUG] Final employee data with managers:', result.map(emp => ({
      full_name: emp.full_name,
      manager_id: emp.manager_id,
      manager_name: emp.manager_name
    })))

    return result
  },

  async getEmployeeById(id: string): Promise<EmployeeWithDepartment | null> {
    // Fetch employee with department only (no manager join due to missing FK constraint)
    const { data: employee, error: employeeError } = await supabaseAdmin.from('appy_employees')
      .select(`
        *,
        appy_departments:department_id (
          id,
          name
        )
      `)
      .eq('id', id)
      .single()
    
    if (employeeError) {
      if (employeeError.code === 'PGRST116') {
        return null // Not found
      }
      // console.error('Failed to fetch employee:', employeeError)
      throw new Error(employeeError.message)
    }

    // Fetch manager separately if employee has one
    let managerName = null
    if (employee.manager_id) {
      const { data: manager, error: managerError } = await supabaseAdmin.from('appy_managers')
        .select('full_name')
        .eq('user_id', employee.manager_id)
        .eq('active', true)
        .single()
      
      if (!managerError && manager) {
        managerName = manager.full_name
      }
    }
    
    return {
      ...employee,
      department_name: employee.appy_departments?.name || '',
      manager_name: managerName
    }
  },

  async createEmployee(employeeData: {
    fullName: string
    compensation: number
    rate: 'hourly' | 'monthly'
    departmentId: string
    managerId?: string
  }): Promise<Employee> {
    const { data, error } = await supabaseAdmin.from('appy_employees')
      .insert({
        full_name: employeeData.fullName,
        compensation: employeeData.compensation,
        rate: employeeData.rate,
        department_id: employeeData.departmentId,
        manager_id: employeeData.managerId || null
      })
      .select()
      .single()
    
    if (error) {
      // console.error('Failed to create employee:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async updateEmployee(id: string, employeeData: {
    fullName?: string
    compensation?: number
    rate?: 'hourly' | 'monthly'
    departmentId?: string
    managerId?: string
    active?: boolean
  }): Promise<Employee> {
    const updateData: any = {}
    
    if (employeeData.fullName !== undefined) updateData.full_name = employeeData.fullName
    if (employeeData.compensation !== undefined) updateData.compensation = employeeData.compensation
    if (employeeData.rate !== undefined) updateData.rate = employeeData.rate
    if (employeeData.departmentId !== undefined) updateData.department_id = employeeData.departmentId
    if (employeeData.managerId !== undefined) updateData.manager_id = employeeData.managerId
    if (employeeData.active !== undefined) updateData.active = employeeData.active
    
    if (Object.keys(updateData).length === 0) {
      throw new Error('No fields to update')
    }
    
    const { data, error } = await supabaseAdmin.from('appy_employees')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()
    
    if (error) {
      // console.error('Failed to update employee:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async softDeleteEmployee(id: string): Promise<void> {
    const { error } = await supabaseAdmin.from('appy_employees')
      .update({ active: false })
      .eq('id', id)
    
    if (error) {
      // console.error('Failed to soft delete employee:', error)
      throw new Error(error.message)
    }
  },

  // Managers
  async getManagers(): Promise<Manager[]> {
    const { data, error } = await supabaseAdmin.from('appy_managers')
      .select('user_id, full_name, email, department_id, active, created_at')
      .eq('active', true)
      .order('full_name')
    
    if (error) {
      // console.error('Failed to fetch managers:', error)
      throw new Error(error.message)
    }
    
    return data || []
  },

  async createManager(userId: string, fullName: string, email: string, departmentId?: string): Promise<Manager> {
    const { data, error } = await supabaseAdmin.from('appy_managers')
      .insert({
        user_id: userId,
        full_name: fullName,
        email: email,
        department_id: departmentId || null
      })
      .select()
      .single()
    
    if (error) {
      // console.error('Failed to create manager:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async ensureManagerExists(userId: string, fullName: string, email: string): Promise<void> {
    // Check if manager already exists
    const { data: existingManager } = await supabaseAdmin.from('appy_managers')
      .select('user_id')
      .eq('user_id', userId)
      .single()
    
    // If manager doesn't exist, create them
    if (!existingManager) {
      const { error } = await supabaseAdmin.from('appy_managers')
        .insert({
          user_id: userId,
          full_name: fullName,
          email: email,
          active: true
        })
      
      if (error) {
        // console.error('Failed to create manager:', error)
        throw new Error(`Failed to create manager: ${error.message}`)
      }
    }
  },

  // Appraisal Periods
  async getAppraisalPeriods(): Promise<AppraisalPeriod[]> {
    const { data, error } = await supabaseAdmin.from('appy_appraisal_periods')
      .select('id, name, start_date, end_date, status, created_at')
      .order('start_date', { ascending: false })
    
    if (error) {
      // console.error('Failed to fetch appraisal periods:', error)
      throw new Error(error.message)
    }
    
    return data || []
  },

  async createAppraisalPeriod(periodData: {
    name: string
    startDate: string
    endDate: string
    status?: string
  }): Promise<AppraisalPeriod> {
    const { data, error } = await supabaseAdmin.from('appy_appraisal_periods')
      .insert({
        name: periodData.name,
        start_date: periodData.startDate,
        end_date: periodData.endDate,
        status: periodData.status || 'draft'
      })
      .select()
      .single()
    
    if (error) {
      // console.error('Failed to create appraisal period:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async updateAppraisalPeriod(id: string, periodData: {
    name?: string
    startDate?: string
    endDate?: string
    status?: string
  }): Promise<AppraisalPeriod> {
    const updateData: any = {}
    
    if (periodData.name !== undefined) updateData.name = periodData.name
    if (periodData.startDate !== undefined) updateData.start_date = periodData.startDate
    if (periodData.endDate !== undefined) updateData.end_date = periodData.endDate
    if (periodData.status !== undefined) updateData.status = periodData.status
    
    if (Object.keys(updateData).length === 0) {
      throw new Error('No fields to update')
    }
    
    const { data, error } = await supabaseAdmin.from('appy_appraisal_periods')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()
    
    if (error) {
      // console.error('Failed to update appraisal period:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  // Appraisals
  async getAppraisalByEmployeeId(employeeId: string, periodId?: string): Promise<Appraisal | null> {
    let query = supabaseAdmin.from('appy_appraisals')
      .select('*')
      .eq('employee_id', employeeId)
      .order('created_at', { ascending: false })
    
    if (periodId) {
      query = query.eq('period_id', periodId)
    }
    
    const { data, error } = await query.limit(1).single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      // console.error('Failed to fetch appraisal:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async saveAppraisalDraft(appraisalData: {
    periodId: string
    employeeId: string
    managerId: string
    question1?: string | null
    question2?: string | null
    question3?: string | null
    question4?: string | null
    question5?: string | null
  }): Promise<Appraisal> {
    const { data, error } = await supabaseAdmin.from('appy_appraisals')
      .upsert({
        period_id: appraisalData.periodId,
        employee_id: appraisalData.employeeId,
        manager_id: appraisalData.managerId,
        question_1: appraisalData.question1 || null,
        question_2: appraisalData.question2 || null,
        question_3: appraisalData.question3 || null,
        question_4: appraisalData.question4 || null,
        question_5: appraisalData.question5 || null,
        status: 'pending'
      }, {
        onConflict: 'period_id,employee_id'
      })
      .select()
      .single()
    
    if (error) {
      // console.error('Failed to save appraisal draft:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async submitAppraisal(id: string): Promise<Appraisal> {
    const { data, error } = await supabaseAdmin.from('appy_appraisals')
      .update({
        status: 'submitted',
        submitted_at: new Date().toISOString(),
        last_edited_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()
    
    if (error) {
      // console.error('Failed to submit appraisal:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  // Revision Functions
  async createAppraisalRevision(originalAppraisalId: string, newData: {
    question1?: string | null
    question2?: string | null
    question3?: string | null
    question4?: string | null
    question5?: string | null
  }): Promise<Appraisal> {
    // First, get the original appraisal
    const { data: originalAppraisal, error: fetchError } = await supabaseAdmin
      .from('appy_appraisals')
      .select('*')
      .eq('id', originalAppraisalId)
      .single()
    
    if (fetchError || !originalAppraisal) {
      throw new Error('Original appraisal not found')
    }

    // Check if this is the first revision (need to set original_submission_date)
    const isFirstRevision = !originalAppraisal.is_revision && originalAppraisal.status === 'submitted'
    const newRevisionNumber = (originalAppraisal.revision_number || 1) + 1

    // Update the original appraisal to create a revision
    const updateData: any = {
      question_1: newData.question1 !== undefined ? newData.question1 : originalAppraisal.question_1,
      question_2: newData.question2 !== undefined ? newData.question2 : originalAppraisal.question_2,
      question_3: newData.question3 !== undefined ? newData.question3 : originalAppraisal.question_3,
      question_4: newData.question4 !== undefined ? newData.question4 : originalAppraisal.question_4,
      question_5: newData.question5 !== undefined ? newData.question5 : originalAppraisal.question_5,
      status: 'pending', // Reset to draft status for editing
      revision_number: newRevisionNumber,
      is_revision: true,
      last_edited_at: new Date().toISOString(),
      submitted_at: null // Clear submitted timestamp
    }

    // Set original submission date if this is the first revision
    if (isFirstRevision) {
      updateData.original_submission_date = originalAppraisal.submitted_at
    }

    const { data, error } = await supabaseAdmin.from('appy_appraisals')
      .update(updateData)
      .eq('id', originalAppraisalId)
      .select()
      .single()
    
    if (error) {
      throw new Error(`Failed to create appraisal revision: ${error.message}`)
    }
    
    return data
  },

  async updateAppraisalRevision(id: string, updateData: {
    question1?: string | null
    question2?: string | null
    question3?: string | null
    question4?: string | null
    question5?: string | null
  }): Promise<Appraisal> {
    const { data, error } = await supabaseAdmin.from('appy_appraisals')
      .update({
        question_1: updateData.question1,
        question_2: updateData.question2,
        question_3: updateData.question3,
        question_4: updateData.question4,
        question_5: updateData.question5,
        last_edited_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update appraisal revision: ${error.message}`)
    }

    return data
  },

  async resubmitAppraisalRevision(id: string): Promise<Appraisal> {
    const { data, error } = await supabaseAdmin.from('appy_appraisals')
      .update({
        status: 'submitted',
        submitted_at: new Date().toISOString(),
        last_edited_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to resubmit appraisal revision: ${error.message}`)
    }

    return data
  },

  // Performance Statistics
  async getPerformanceStatsByPeriod(periodId: string): Promise<Appraisal[]> {
    const { data, error } = await supabaseAdmin.from('appy_appraisals')
      .select('*')
      .eq('period_id', periodId)
      .order('created_at', { ascending: false })
    
    if (error) {
      // console.error('Failed to fetch performance statistics:', error)
      throw new Error(error.message)
    }
    
    return data || []
  },

  async getAllAppraisalsForManager(managerId: string, periodId?: string): Promise<Appraisal[]> {
    let query = supabaseAdmin.from('appy_appraisals')
      .select('*')
      .eq('manager_id', managerId)
      .order('created_at', { ascending: false })
    
    if (periodId) {
      query = query.eq('period_id', periodId)
    }
    
    const { data, error } = await query
    
    if (error) {
      // console.error('Failed to fetch manager appraisals:', error)
      throw new Error(error.message)
    }
    
    return data || []
  },

  async getAppraisalsWithEmployeeData(periodId?: string): Promise<AppraisalWithDetails[]> {
    let query = supabaseAdmin.from('appy_appraisals')
      .select(`
        *,
        appy_employees!inner (
          id,
          full_name,
          compensation,
          rate,
          appy_departments!inner (
            name
          )
        ),
        appy_managers!inner (
          full_name
        )
      `)
      .order('created_at', { ascending: false })
    
    if (periodId) {
      query = query.eq('period_id', periodId)
    }
    
    const { data, error } = await query
    
    if (error) {
      // console.error('Failed to fetch appraisals with employee data:', error)
      throw new Error(error.message)
    }
    
    return (data || []).map(appraisal => ({
      ...appraisal,
      employee_name: appraisal.appy_employees?.full_name || '',
      manager_name: appraisal.appy_managers?.full_name || '',
      department_name: appraisal.appy_employees?.appy_departments?.name || ''
    }))
  },

  // Get all submitted appraisals for a period
  async getSubmittedAppraisals(periodId: string): Promise<Appraisal[]> {
    const { data, error } = await supabaseAdmin.from('appy_appraisals')
      .select('*')
      .eq('period_id', periodId)
      .eq('status', 'submitted')

    if (error) {
      // console.error('Failed to fetch submitted appraisals:', error)
      throw new Error(error.message)
    }

    return data || []
  },

  // Update appraisal status (for approvals/rejections)
  async updateAppraisalStatus(
    appraisalId: string, 
    status: 'approved' | 'rejected', 
    updatedBy: string, 
    reason?: string
  ): Promise<boolean> {
    const updateData: any = {
      status,
      updated_at: new Date().toISOString(),
    }

    if (status === 'approved') {
      updateData.approved_by = updatedBy
      updateData.approved_at = new Date().toISOString()
    } else if (status === 'rejected') {
      updateData.rejected_by = updatedBy
      updateData.rejected_at = new Date().toISOString()
      if (reason) {
        updateData.rejected_reason = reason
      }
    }

    const { error } = await supabaseAdmin.from('appy_appraisals')
      .update(updateData)
      .eq('id', appraisalId)

    if (error) {
      // console.error('Failed to update appraisal status:', error)
      throw new Error(error.message)
    }

    return true
  },

  // PTO (Paid Time Off) Functions
  async getPTOBalance(employeeId: string, year?: number): Promise<any> {
    const currentYear = year || new Date().getFullYear()
    
    const { data, error } = await supabaseAdmin
      .from('appy_employee_pto_balances')
      .select('*')
      .eq('employee_id', employeeId)
      .eq('year', currentYear)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        // No balance record found, create one with default 7 days
        return await this.createPTOBalance(employeeId, currentYear)
      }
      // console.error('Failed to fetch PTO balance:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async createPTOBalance(employeeId: string, year: number, totalDays: number = 7): Promise<any> {
    const { data, error } = await supabaseAdmin
      .from('appy_employee_pto_balances')
      .insert({
        employee_id: employeeId,
        year: year,
        total_days: totalDays,
        used_days: 0
      })
      .select()
      .single()
    
    if (error) {
      // console.error('Failed to create PTO balance:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async updatePTOBalance(employeeId: string, usedDays: number, year?: number): Promise<any> {
    const currentYear = year || new Date().getFullYear()
    
    const { data, error } = await supabaseAdmin
      .from('appy_employee_pto_balances')
      .update({
        used_days: usedDays,
        updated_at: new Date().toISOString()
      })
      .eq('employee_id', employeeId)
      .eq('year', currentYear)
      .select()
      .single()
    
    if (error) {
      // console.error('Failed to update PTO balance:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async createPTORequest(requestData: {
    employeeId: string
    managerId: string
    requestType: 'vacation' | 'sick' | 'personal' | 'emergency'
    startDate: string
    endDate: string
    daysRequested: number
    reason?: string
  }): Promise<any> {
    const { data, error } = await supabaseAdmin
      .from('appy_pto_requests')
      .insert({
        employee_id: requestData.employeeId,
        manager_id: requestData.managerId,
        request_type: requestData.requestType,
        start_date: requestData.startDate,
        end_date: requestData.endDate,
        days_requested: requestData.daysRequested,
        reason: requestData.reason || null,
        status: 'pending'
      })
      .select()
      .single()
    
    if (error) {
      // console.error('Failed to create PTO request:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async getPTORequests(filters?: {
    employeeId?: string
    managerId?: string
    status?: 'pending' | 'approved' | 'rejected' | 'cancelled'
    year?: number
  }): Promise<any[]> {
    let query = supabaseAdmin
      .from('appy_pto_requests')
      .select(`
        *,
        appy_employees!inner (
          id,
          full_name,
          appy_departments!inner (
            name
          )
        )
      `)
      .order('created_at', { ascending: false })
    
    if (filters?.employeeId) {
      query = query.eq('employee_id', filters.employeeId)
    }
    
    if (filters?.managerId) {
      query = query.eq('manager_id', filters.managerId)
    }
    
    if (filters?.status) {
      query = query.eq('status', filters.status)
    }
    
    if (filters?.year) {
      const startOfYear = `${filters.year}-01-01`
      const endOfYear = `${filters.year}-12-31`
      query = query.gte('start_date', startOfYear).lte('start_date', endOfYear)
    }
    
    const { data, error } = await query
    
    if (error) {
      // console.error('Failed to fetch PTO requests:', error)
      throw new Error(error.message)
    }
    
    return (data || []).map(request => ({
      ...request,
      employee_name: request.appy_employees?.full_name || '',
      manager_name: 'Manager', // Remove manager join for now
      department_name: request.appy_employees?.appy_departments?.name || ''
    }))
  },

  async getPTORequestById(requestId: string): Promise<any> {
    const { data, error } = await supabaseAdmin
      .from('appy_pto_requests')
      .select(`
        *,
        appy_employees!inner (
          id,
          full_name,
          appy_departments!inner (
            name
          )
        ),
        appy_managers!inner (
          full_name
        )
      `)
      .eq('id', requestId)
      .single()
    
    if (error) {
      // console.error('Failed to fetch PTO request:', error)
      throw new Error(error.message)
    }
    
    return {
      ...data,
      employee_name: data.appy_employees?.full_name || '',
      manager_name: data.appy_managers?.full_name || '',
      department_name: data.appy_employees?.appy_departments?.name || ''
    }
  },

  async approvePTORequest(requestId: string, approverId: string): Promise<any> {
    const { data, error } = await supabaseAdmin
      .from('appy_pto_requests')
      .update({
        status: 'approved',
        approved_by: approverId,
        approved_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId)
      .select()
      .single()
    
    if (error) {
      // console.error('Failed to approve PTO request:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async rejectPTORequest(requestId: string, approverId: string, reason: string): Promise<any> {
    const { data, error } = await supabaseAdmin
      .from('appy_pto_requests')
      .update({
        status: 'rejected',
        approved_by: approverId,
        approved_at: new Date().toISOString(),
        rejected_reason: reason,
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId)
      .select()
      .single()
    
    if (error) {
      // console.error('Failed to reject PTO request:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async cancelPTORequest(requestId: string): Promise<any> {
    const { data, error } = await supabaseAdmin
      .from('appy_pto_requests')
      .update({
        status: 'cancelled',
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId)
      .select()
      .single()
    
    if (error) {
      // console.error('Failed to cancel PTO request:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async getPTOStats(managerId?: string, year?: number): Promise<any> {
    const currentYear = year || new Date().getFullYear()
    
    let query = supabaseAdmin
      .from('appy_pto_requests')
      .select('status')
      .gte('start_date', `${currentYear}-01-01`)
      .lte('start_date', `${currentYear}-12-31`)
    
    if (managerId) {
      query = query.eq('manager_id', managerId)
    }
    
    const { data, error } = await query
    
    if (error) {
      // console.error('Failed to fetch PTO stats:', error)
      throw new Error(error.message)
    }
    
    const stats = {
      totalRequests: data?.length || 0,
      pendingRequests: data?.filter(r => r.status === 'pending').length || 0,
      approvedRequests: data?.filter(r => r.status === 'approved').length || 0,
      rejectedRequests: data?.filter(r => r.status === 'rejected').length || 0,
    }
    
    return stats
  },

  async checkPTOAvailability(employeeId: string, daysRequested: number, year?: number): Promise<boolean> {
    const currentYear = year || new Date().getFullYear()
    
    try {
      const balance = await this.getPTOBalance(employeeId, currentYear)
      return balance.available_days >= daysRequested
    } catch (error) {
      // console.error('Failed to check PTO availability:', error)
      return false
    }
  },

  async initializePTOBalances(employeeIds: string[], year?: number, totalDays: number = 7): Promise<void> {
    const currentYear = year || new Date().getFullYear()
    
    const balances = employeeIds.map(employeeId => ({
      employee_id: employeeId,
      year: currentYear,
      total_days: totalDays,
      used_days: 0
    }))
    
    const { error } = await supabaseAdmin
      .from('appy_employee_pto_balances')
      .upsert(balances, {
        onConflict: 'employee_id,year'
      })
    
    if (error) {
      // console.error('Failed to initialize PTO balances:', error)
      throw new Error(error.message)
    }
  },

  // Appraisal Template Functions
  async getTemplates(filters?: {
    departmentId?: string
    isActive?: boolean
    createdBy?: string
  }): Promise<any[]> {
    let query = supabaseAdmin
      .from('appy_appraisal_templates')
      .select(`
        *,
        appy_departments (
          name
        )
      `)
      .order('is_default', { ascending: false })
      .order('name', { ascending: true })
    
    if (filters?.departmentId) {
      query = query.eq('department_id', filters.departmentId)
    }
    
    if (filters?.isActive !== undefined) {
      query = query.eq('is_active', filters.isActive)
    }
    
    if (filters?.createdBy) {
      query = query.eq('created_by', filters.createdBy)
    }
    
    const { data, error } = await query
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  },

  async getTemplateById(id: string): Promise<any | null> {
    const { data, error } = await supabaseAdmin
      .from('appy_appraisal_templates')
      .select(`
        *,
        appy_departments (
          name
        )
      `)
      .eq('id', id)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') return null // Not found
      throw new Error(error.message)
    }
    
    return data
  },

  async getDefaultTemplate(departmentId?: string): Promise<any | null> {
    let query = supabaseAdmin
      .from('appy_appraisal_templates')
      .select(`
        *,
        appy_departments (
          name
        )
      `)
      .eq('is_active', true)
      .eq('is_default', true)
    
    if (departmentId) {
      query = query.or(`department_id.eq.${departmentId},department_id.is.null`)
    }
    
    const { data, error } = await query
      .order('department_id', { ascending: false, nullsFirst: false })
      .limit(1)
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data?.[0] || null
  },

  async createTemplate(templateData: {
    name: string
    description?: string
    questions: any
    departmentId?: string
    roleFilter?: string
    isActive?: boolean
    isDefault?: boolean
    createdBy: string
  }): Promise<any> {
    const { data, error } = await supabaseAdmin
      .from('appy_appraisal_templates')
      .insert({
        name: templateData.name,
        description: templateData.description,
        questions: templateData.questions,
        department_id: templateData.departmentId,
        role_filter: templateData.roleFilter,
        is_active: templateData.isActive ?? true,
        is_default: templateData.isDefault ?? false,
        created_by: templateData.createdBy
      })
      .select()
      .single()
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data
  },

  async updateTemplate(id: string, templateData: {
    name?: string
    description?: string
    questions?: any
    departmentId?: string
    roleFilter?: string
    isActive?: boolean
    isDefault?: boolean
  }): Promise<any> {
    const updateData: any = {}
    
    if (templateData.name !== undefined) updateData.name = templateData.name
    if (templateData.description !== undefined) updateData.description = templateData.description
    if (templateData.questions !== undefined) updateData.questions = templateData.questions
    if (templateData.departmentId !== undefined) updateData.department_id = templateData.departmentId
    if (templateData.roleFilter !== undefined) updateData.role_filter = templateData.roleFilter
    if (templateData.isActive !== undefined) updateData.is_active = templateData.isActive
    if (templateData.isDefault !== undefined) updateData.is_default = templateData.isDefault
    
    const { data, error } = await supabaseAdmin
      .from('appy_appraisal_templates')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data
  },

  async deleteTemplate(id: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('appy_appraisal_templates')
      .delete()
      .eq('id', id)
    
    if (error) {
      throw new Error(error.message)
    }
  },

  async duplicateTemplate(id: string, newName: string, createdBy: string): Promise<any> {
    // Get the original template
    const original = await this.getTemplateById(id)
    if (!original) {
      throw new Error('Template not found')
    }
    
    // Create a copy
    const { data, error } = await supabaseAdmin
      .from('appy_appraisal_templates')
      .insert({
        name: newName,
        description: original.description,
        questions: original.questions,
        department_id: original.department_id,
        role_filter: original.role_filter,
        is_active: true,
        is_default: false,
        created_by: createdBy
      })
      .select()
      .single()
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data
  },

  async trackTemplateUsage(templateId: string, periodId: string, managerId: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('appy_template_usage')
      .upsert({
        template_id: templateId,
        period_id: periodId,
        manager_id: managerId,
        usage_count: 1
      }, {
        onConflict: 'template_id,period_id,manager_id'
      })
    
    if (error) {
      throw new Error(error.message)
    }
  },

  async getTemplateUsageStats(templateId?: string): Promise<any[]> {
    let query = supabaseAdmin
      .from('appy_template_usage')
      .select(`
        *,
        appy_appraisal_templates (
          name
        ),
        appy_appraisal_periods (
          name,
          start_date,
          end_date
        )
      `)
      .order('created_at', { ascending: false })
    
    if (templateId) {
      query = query.eq('template_id', templateId)
    }
    
    const { data, error } = await query
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  },

  // Employee-Manager relationship functions
  async getEmployeeManagers(employeeId: string): Promise<EmployeeManager[]> {
    const { data, error } = await supabaseAdmin.from('appy_employee_managers')
      .select(`
        *,
        appy_managers:manager_id (
          full_name
        )
      `)
      .eq('employee_id', employeeId)
      .order('is_primary', { ascending: false })
    
    if (error) {
      throw new Error(error.message)
    }
    
    return (data || []).map(em => ({
      id: em.id,
      employeeId: em.employee_id,
      managerId: em.manager_id,
      managerName: em.appy_managers?.full_name || '',
      isPrimary: em.is_primary,
      assignedAt: em.assigned_at || em.created_at
    }))
  },

  async assignManagerToEmployee(employeeId: string, managerId: string, isPrimary = false): Promise<EmployeeManager> {
    // If setting as primary, remove primary flag from other managers first
    if (isPrimary) {
      await supabaseAdmin.from('appy_employee_managers')
        .update({ is_primary: false })
        .eq('employee_id', employeeId)
    }

    const { data, error } = await supabaseAdmin.from('appy_employee_managers')
      .insert({
        employee_id: employeeId,
        manager_id: managerId,
        is_primary: isPrimary,
        assigned_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      throw new Error(error.message)
    }
    
    return {
      id: data.id,
      employeeId: data.employee_id,
      managerId: data.manager_id,
      managerName: '',
      isPrimary: data.is_primary,
      assignedAt: data.assigned_at || data.created_at
    }
  },

  async removeManagerFromEmployee(employeeId: string, managerId: string): Promise<void> {
    const { error } = await supabaseAdmin.from('appy_employee_managers')
      .delete()
      .eq('employee_id', employeeId)
      .eq('manager_id', managerId)
    
    if (error) {
      throw new Error(error.message)
    }
  },

  async getAppraisalCompletionStatus(employeeId: string, periodId: string): Promise<{
    totalManagers: number
    completedManagers: number
    managersStatus: Array<{
      managerId: string
      managerName: string
      status: string
      submittedAt?: string
      isPrimary: boolean
    }>
    allManagersCompleted: boolean
  }> {
    // Get all managers for this employee
    const managers = await this.getEmployeeManagers(employeeId)
    
    if (managers.length === 0) {
      return {
        totalManagers: 0,
        completedManagers: 0,
        managersStatus: [],
        allManagersCompleted: false
      }
    }

    // Get appraisal status for each manager
    const managersStatus = await Promise.all(
      managers.map(async (manager) => {
        const { data: appraisal } = await supabaseAdmin.from('appy_appraisals')
          .select('status, submitted_at')
          .eq('employee_id', employeeId)
          .eq('period_id', periodId)
          .eq('manager_id', manager.managerId)
          .single()

        return {
          managerId: manager.managerId,
          managerName: manager.managerName,
          status: appraisal?.status || 'not-started',
          submittedAt: appraisal?.submitted_at,
          isPrimary: manager.isPrimary
        }
      })
    )

    const completedManagers = managersStatus.filter(
      m => m.status === 'submitted' || m.status === 'approved'
    ).length

    return {
      totalManagers: managers.length,
      completedManagers,
      managersStatus,
      allManagersCompleted: completedManagers === managers.length
    }
  }
}

// console.log('📊 Database module loaded successfully')